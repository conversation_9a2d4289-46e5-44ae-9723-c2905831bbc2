import re
import json


def json_to_html_table(data):
    html = """
    <table class="hardware-table">
    """
    for key, value in data.items():
        html += '<tr>'
        html += f'<th>{key}</th>'
        
        if isinstance(value, list):
            if len(value) == 0:
                value_str = "无"
            else:
                value_str = '<br>'.join(value)
        else:
            value_str = str(value)
            
        html += f'<td>{value_str}</td>'
        html += '</tr>'
    html += '</table>'
    return html


def read_json_from_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.loads(f.read())
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except json.JSONDecodeError:
        print(f"错误：文件 {file_path} 包含无效的JSON数据")
        return None

def add_hardware_config_info():
    json_data = read_json_from_file("./hardware_config.txt")
    contents = json_to_html_table(json_data)
    with open("./autologs/statistics.html", 'r', encoding='utf-8') as file:
        html_content = file.read()
    updated_content = re.sub(r'###hardware_config_table###', contents, html_content)
    with open("./autologs/statistics.html", 'w', encoding='utf-8') as file:
        file.write(updated_content)


if __name__ == "__main__":
    add_hardware_config_info()

