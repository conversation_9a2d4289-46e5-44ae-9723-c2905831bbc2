import re
import requests

from src.utils.myftp import myFtp


def get_log_file_ftp_path(runId):
    url = f'https://wxict.zte.com.cn:8000/testtask/case/list?end_time&page=1&record_type=0&run_id={runId}&size=100&start_time'
    headers = {
        "token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7KqYjtCVeNgRm3mK0cOVv81lEa_RcRQ5Rumyjzro1cM",
        'Accept': 'application/json',
    }
    response = requests.get(url, headers=headers)

    ftpPaths = []
    if response.status_code == 200:
        data = response.json()
        for item in data["data"]:
            fd = re.search("(/report/auto/\d+/\d+)/\d+/[log|report]+.html", item["report_url"])[1]
            if fd and fd not in ftpPaths:
                ftpPaths.append(fd)
    else:
        print(f'Error: {response.status_code} - {response.text}')
    return ftpPaths


def download_files_from_ftp(localPath, romtePath):
    ftp = myFtp()
    ftp.Login()
    ftp.download_file_tree(localPath, romtePath, excludeDirs=["errLogsCollect"])
    ftp.close()
    print(f"下载 {romtePath} 完成")


def download_testcase_log_by_run_id(runId, localPath = "./autologs"):
    ftpPaths = get_log_file_ftp_path(runId)
    for ftpPath in ftpPaths:
        download_files_from_ftp(localPath ,ftpPath)
