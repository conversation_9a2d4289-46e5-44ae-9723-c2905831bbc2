import os
import platform
import subprocess
import fnmatch


def delete_contents(foldePath):
    try:
        if platform.system() == 'Windows':
            subprocess.run(['rmdir', '/s', '/q', foldePath], check=True)
        else:
            subprocess.run(['rm', '-rf', f'{foldePath}/'], check=True)
        print(f"成功删除 {foldePath} 下的所有内容。")
    except subprocess.CalledProcessError as e:
        print(f"删除失败: {e}")


def delete_html_files(folderPath):
    for filename in os.listdir(folderPath):
        if filename.endswith('.html'):
            filePath = os.path.join(folderPath, filename)
            try:
                os.remove(filePath)
                print(f"已删除文件: {filePath}")
            except Exception as e:
                print(f"删除文件 {filePath} 失败: {e}")

def delete_xlsx_files(folderPath):
    for filename in os.listdir(folderPath):
        if filename.endswith('.xlsx'):
            filePath = os.path.join(folderPath, filename)
            try:
                os.remove(filePath)
                print(f"已删除文件: {filePath}")
            except Exception as e:
                print(f"删除文件 {filePath} 失败: {e}")

def delete_xml_files(folder_path):
    for root, dirs, files in os.walk(folder_path):
        for filename in fnmatch.filter(files, '*.xml'):
            file_path = os.path.join(root, filename)
            try:
                os.remove(file_path)
                print(f"已删除文件: {file_path}")
            except Exception as e:
                print(f"删除文件 {file_path} 失败: {e}")


if __name__ == "__main__":
    # folderToClean = '/home/<USER>/Desktop/generate_report/autologs'
    # folderToClean = '/home/<USER>/Desktop/generate_report/R5300G5/autologs'
    # folderToClean = '/home/<USER>/Desktop/generate_report/R5350G5/autologs'
    folderToClean = '/home/<USER>/Desktop/20250221/20250221_BMC04.25.01.10_BIOS14.25.01.10_R5330/autologs'
    delete_xml_files(folderToClean)
