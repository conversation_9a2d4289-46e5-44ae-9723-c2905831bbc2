import os
from openpyxl import Workbook
import openpyxl
from openpyxl.styles import Pattern<PERSON>ill

from openpyxl.styles import Border, Side, PatternFill, Font

green_fill = PatternFill(start_color='00FF00', end_color='00FF00', fill_type='solid')
red_fill = PatternFill(start_color='FF0000', end_color='FF0000', fill_type='solid')
sky_blue_fill1 = PatternFill(start_color='87CEEB', end_color='87CEEB', fill_type='solid')


# 创建边框样式
border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)


def wirte_test_case_result_to_excel(logTestcases, filePath, sheetName, sacIdx, resultIdx, keyInfoMap):
    workbook = openpyxl.load_workbook(filePath)
    sheet = workbook[sheetName]
    for logPath, testcases in logTestcases:
        for sacId, caseInfo in testcases.items():
            # if caseInfo[0] !="PASS":
            #     continue
            for row in range(2, sheet.max_row + 1):
                if sheet.cell(row=row, column=sacIdx).value == sacId:
                    newLogPath = logPath.replace("/autologs_componet", "").replace("/autologs", "")
                    link = f"{newLogPath}#{caseInfo[1]}"
                    displayText = caseInfo[0]
                    sheet.cell(row=row, column=resultIdx).value = displayText
                    sheet.cell(row=row, column=resultIdx).hyperlink = link
                    sheet.cell(row=row, column=resultIdx).style = 'Hyperlink'
                    sheet.cell(row=row, column=resultIdx).fill = green_fill if caseInfo[0] == "PASS" else red_fill
                    sheet.cell(row=row, column=resultIdx).border = border
                    if sacId in keyInfoMap.keys():
                        sheet.cell(row=row, column=9).value = sacId + "_KEY_INFO"
    workbook.save(filePath)
    print("----- write auto test case result finished -----")


def write_attachments_files_to_excel(logTestcasesInfo, resultExcelFile, sheetName, sacIdx):
    workbook = openpyxl.load_workbook(resultExcelFile)
    sheet = workbook[sheetName]
    for logPath, testcases in logTestcasesInfo:
        for sacId, caseInfo in testcases.items():
            for row in range(1, sheet.max_row + 1):
                if sheet.cell(row=row, column=sacIdx).value == sacId:
                    # attachmentPath = logPath.replace("log.html", f"{sacId}.7z")
                    attachmentPath = logPath.replace("log.html", f"attachment")
                    print("attachmentPath", attachmentPath)
                    link = attachmentPath.replace("/autologs_componet", "").replace("/autologs", "")
                    if os.path.exists(attachmentPath):
                        sheet.cell(row=row, column=9).value = "附件链接"
                        sheet.cell(row=row, column=9).hyperlink = link
                        sheet.cell(row=row, column=9).style = 'Hyperlink'
                        sheet.cell(row=row, column=9).border = border
    workbook.save(resultExcelFile)
    print("----- write auto test case attachment finished -----")


def wirte_manual_tc_result_to_excel(manualTcInfo, resultExcelFile, sheetName, sacIdx, resultIdx):
    workbook = openpyxl.load_workbook(resultExcelFile)
    sheet = workbook[sheetName]
    for sacId, logPath in manualTcInfo:
        for row in range(2, sheet.max_row + 1):
            if sheet.cell(row=row, column=sacIdx).value == sacId:
                print(sacId)
                sheet.cell(row=row, column=resultIdx).value = "PASS"
                sheet.cell(row=row, column=resultIdx).hyperlink = f"../manual/{logPath}"
                sheet.cell(row=row, column=resultIdx).style = 'Hyperlink'
                sheet.cell(row=row, column=resultIdx).fill = green_fill
                sheet.cell(row=row, column=resultIdx).border = border
    workbook.save(resultExcelFile)
    print("----- write manual test case result finished -----")
    pass
