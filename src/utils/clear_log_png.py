import os


import os
import re
from bs4 import BeautifulSoup

def get_image_names_from_log(log_path):
    """
    从log.html中提取所有图片的文件名（格式：2024-12-05_08-10-49.png），
    假设这些图片是由JavaScript动态生成的。
    """
    with open(log_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 正则表达式匹配符合格式的图片文件名（2024-12-05_08-10-49.png）
    pattern = r'\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.png'
    # 使用正则从整个HTML文件内容中提取符合条件的图片文件名
    img_names = set(re.findall(pattern, content))
    return img_names


def delete_unmatched_images(screenshots_dir, img_names_from_log):
    """
    删除screenshots文件夹中没有出现在log.html中的图片。
    """
    for file in os.listdir(screenshots_dir):
        file_path = os.path.join(screenshots_dir, file)
        
        # 只处理PNG图片
        if file.endswith('.png') and file not in img_names_from_log:
            print(f"删除文件: {file_path}")
            os.remove(file_path)

def process_autologs(autologs_dir):
    """
    遍历autologs文件夹，处理每个子文件夹中的log.html和screenshots文件夹。
    """
    for root, dirs, files in os.walk(autologs_dir):
        # 只处理包含log.html的目录，并且包含screenshots文件夹
        if 'log.html' in files and 'screenshots' in dirs:
            log_path = os.path.join(root, 'log.html')
            print(log_path)
            screenshots_dir = os.path.join(root, 'screenshots')
            print(screenshots_dir)
            
            # 获取log.html中所有图片的文件名
            img_names_from_log = get_image_names_from_log(log_path)
            print(img_names_from_log)
            
            # 删除screenshots文件夹中没有出现在log.html中的图片
            delete_unmatched_images(screenshots_dir, img_names_from_log)

if __name__ == "__main__":
    autologsDir = "/home/<USER>/Desktop/20250220_BMC04.24.04.30_BIOS04.24.05.10_R6500G5/autologs"
    process_autologs(autologsDir)
