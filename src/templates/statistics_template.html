<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用例执行统计报告</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fa;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #4CAF50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        td {
            background-color: #f9f9f9;
        }
        td.pass {
            background-color: #c8e6c9;
            color: #388e3c;
        }
        td.fail {
            background-color: #ffccbc;
            color: #d32f2f;
        }
        .footer {
            text-align: center;
            font-size: 14px;
            margin-top: 30px;
            color: #777;
        }
        .changelist {
            text-align: center;
            font-size: 14px;
            margin-top: 10px;
            color: #777;
        }
        .container2 {
            width: 80%;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .info-list {
            list-style-type: none; /* 去掉列表的默认样式 */
            padding: 0;
            margin: 0;
            font-family: Arial, sans-serif; /* 设置字体 */
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center; /* 垂直居中对齐 */
            height: 40px; /* 固定高度 */
            border-bottom: 1px solid #ddd; 
            width: 100%; /* 宽度固定为100% */
        }

        .info-label {
            width: 110px;
            font-weight: bold; /* 加粗标签 */
            margin-right: 10px; /* 标签与值之间的间距 */
            font-size: 16px; /* 标签字体大小 */
        }

        .info-value {
            font-size: 15px; /* 值字体大小 */
            color: #333; /* 值的颜色 */
        }
        .info {
            display: inline-block;
            margin: 0 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .hardware-table {
        border-collapse: collapse;
        width: 800px;  /* 设置固定宽度 */
        margin: 20px auto;  /* 居中显示 */
        font-family: Arial, sans-serif;
    }
    .hardware-table th, .hardware-table td {
        border: 1px solid #ddd;
        padding: 12px;
    }
    .hardware-table th {
        background-color: #f5f5f5;
        width: 20%;  /* 第一列宽度 */
        text-align: right;
        color: #333; /* 值的颜色 */

    }
    .hardware-table td {
        width: 80%;  /* 第二列宽度 */
        text-align: left;
    }
    .hardware-table tr:hover {
        background-color: #f9f9f9;
    }
    </style>
</head>
<body>
    <div class="container">
        <h1>用例执行统计报告</h1>
        <div class="changelist">
            ###changeListData###
        </div>
        <table>
            <thead>
                <tr>
                    <th>测试项</th>
                    <th>用例执行总数</th>
                    <th>通过数</th>
                    <th>失败数</th>
                    <th>通过率</th>
                </tr>
            </thead>
            <tbody>
                ###statisticsData###
            </tbody>
        </table>
    </div>
    <div class="container">
        <h1>环境配置信息</h1>
        ###hardware_config_table###
    </div>
</body>
</html>
