import os
from pydocx import PyDocX


def convert_docx_to_html(manualLogPath):
    for fileName in os.listdir(manualLogPath):
        print(fileName)
        if fileName.startswith("SAC-") and fileName.endswith(".docx"):
            print(fileName)
            htmlContent = PyDocX.to_html(os.path.join(manualLogPath, fileName))
            with open(os.path.join(manualLogPath, fileName.replace("docx", "html")), 'w', encoding="utf-8") as f:
                f.write(htmlContent)
    print("-------- end --------")


# convert_docx_to_html("/home/<USER>/Desktop/generate_report/manual")
convert_docx_to_html("/home/<USER>/Desktop/新建文件夹")
