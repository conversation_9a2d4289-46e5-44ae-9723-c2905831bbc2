
import re
import os
import json

from bs4 import BeautifulSoup

def remove_underscores_from_links(html_file_path):
    # 读取HTML文件
    with open(html_file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()

    # 解析HTML内容
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找所有的<a>标签
    for a_tag in soup.find_all('a'):
        # 添加或修改style属性，移除下划线
        a_tag['style'] = a_tag.get('style', '') + 'text-decoration: none;'

    # 将修改后的HTML内容写回原文件
    with open(html_file_path, 'w', encoding='utf-8') as file:
        file.write(str(soup))


def remove_underscores_from_links_by_logdir(logDir):
    for filename in os.listdir(logDir):
        if filename.endswith('.html'):
            remove_underscores_from_links(os.path.join(logDir, filename))

def generate_index_html(logDir='./autologs', outputFilePath="./index.html"):
    remove_underscores_from_links_by_logdir(logDir)
    contents = generate_content(logDir)
    replace_html_content(contents, outputFilePath)
    print("------ index.html 生成完成 ------")

def get_html_file_names(dir="./autologs"):
    htmlFiles = []
    pattern = re.compile(r'_(\d+\..*)-\d+\.html$')
    for filename in os.listdir(dir):
        if filename.endswith('.html'):
            match = pattern.search(filename)
            if match:
                matchContent = match.group(1)
                htmlFiles.append((matchContent, os.path.join(dir, filename)))
    sortedFiles = sorted(htmlFiles, key=lambda x: float(x[0].split('.')[0]))
    return sortedFiles

def generate_content(logDir):
    htmlFiles = get_html_file_names(logDir)
    contents = []
    if os.path.exists("./autologs/statistics.html"):
        contents.append("""<li onclick="loadContent('./autologs/statistics.html', this)">用例执行统计报告</li>""")
    for sheetName, htmlPath in htmlFiles:
        contents.append(f"""<li onclick="loadContent('{htmlPath}', this)">{sheetName}</li>""")
    return "\n".join(contents)

def replace_html_content(newContent, outputFilePath="./index.html", inputFilePath='./src/templates/index_template.html'):
    with open(inputFilePath, 'r', encoding='utf-8') as file:
        html_content = file.read()
    updated_content = re.sub(r'###fileList###', newContent, html_content)
    with open(outputFilePath, 'w', encoding='utf-8') as file:
        file.write(updated_content)


if __name__ == "__main__":
    # generate_index_html()
    remove_underscores_from_links("/home/<USER>/Desktop/report_component_12131109/autologs_componet/京东云服务器部件测试_6.网卡-241213110154.html")
