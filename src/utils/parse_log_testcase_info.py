import os
import re
from robot.api import ExecutionResult
from robot.api import ResultVisitor
from pydocx import PyDocX


class ExecutionAnalysisVisitor(ResultVisitor):

    def __init__(self) -> None:
        super().__init__()
        self.tests = {}

    def visit_test(self, test):
        sacId = re.search("^(SAC-\d+|SSP-\d+)", test.name)[1] if re.search("^(SAC-\d+|SSP-\d+)", test.name) else None
        if sacId:
            self.tests.update({sacId: (test.status, test.id)})

def get_test_case_info(xmlFilePath):
    result = ExecutionResult(xmlFilePath)
    visitor = ExecutionAnalysisVisitor()
    result.visit(visitor)
    return visitor.tests

def find_valid_folders(rootPath="./autologs"):
    validFiles = []
    for root, _, files in os.walk(rootPath):
        if root.count(os.sep) == rootPath.count(os.sep) + 1:
            hasOutput = 'output.xml' in files
            hasLog = 'log.html' in files
            if hasOutput and hasLog:
                outputPath = os.path.join(root, 'output.xml')
                logPath = os.path.join(root, 'log.html')
                validFiles.append((outputPath, logPath))
    return validFiles

def get_log_testcase_info(autologPath):
    validFiles = find_valid_folders(autologPath)
    logTestcasesInfo = []
    for xmlFile, logFile in validFiles:
        testcasesInfo = get_test_case_info(xmlFile)
        logTestcasesInfo.append((logFile, testcasesInfo))
    return logTestcasesInfo


def get_manual_testcase_info(manualLogPath):
    manualTcInfo = []
    for fileName in os.listdir(manualLogPath):
        if fileName.startswith("SAC-"):
            manualTcInfo.append((fileName.replace(".html", "").replace(".zip", ""), fileName))
    return manualTcInfo
