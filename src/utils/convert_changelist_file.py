import os
import xlsx2html


def convert_xlsx_to_html(dirPath):
    for filename in os.listdir(dirPath):
        if filename.endswith('.xlsx'):
            xlsx2html.xlsx2html(os.path.join(dirPath, filename), os.path.join(dirPath, filename.replace("xlsx", "html")))


if __name__ == "__main__":
    convert_xlsx_to_html("/home/<USER>/Desktop/generate_report_baidu/changelist")
    print("------- end -------")