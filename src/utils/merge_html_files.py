from bs4 import BeautifulSoup


def read_html(file_path):
    """读取HTML文件并返回BeautifulSoup对象"""
    with open(file_path, 'r', encoding='utf-8') as file:
        return BeautifulSoup(file, 'lxml')


def extract_styles_and_links(soup):
    """提取样式和链接标签"""
    styles_and_links = {'style': [], 'link': []}
    for style_tag in soup.find_all('style'):
        styles_and_links['style'].append(style_tag.extract())
    for link_tag in soup.find_all('link', {'rel': 'stylesheet'}):
        styles_and_links['link'].append(link_tag.extract())
    return styles_and_links


def merge_bodies(html_files):
    """合并多个HTML文件的内容"""
    merged_html = BeautifulSoup('<html></html>', 'lxml')

    # 创建一个新的<head>部分
    new_head = merged_html.new_tag('head')
    
    # 收集所有样式和链接
    all_styles = []
    all_links = []
    for html_file in html_files:
        soup = read_html(html_file)
        styles_and_links = extract_styles_and_links(soup)
        all_styles.extend(styles_and_links['style'])
        all_links.extend(styles_and_links['link'])

    # 将所有样式和链接添加到<head>中
    for tag in all_styles + all_links:
        new_head.append(tag)

    merged_html.html.append(new_head)

    # 创建一个新的<body>部分
    new_body = merged_html.new_tag('body')

    for html_file in html_files:
        soup = read_html(html_file)
        body = soup.find('body')

        # 创建容器div以包裹每个HTML文件的内容
        container_div = merged_html.new_tag('div', attrs={'class': 'container'})
        for element in body.contents:
            if isinstance(element, str) and element.strip():
                container_div.append(element.strip())
            elif element.name:
                container_div.append(element.extract())
        
        new_body.append(container_div)

    merged_html.html.append(new_body)

    return merged_html


def write_new_html(new_body, output_file):
    """将合并后的HTML内容写入文件"""
    new_html = f'<!DOCTYPE html>\n<html>\n{new_body.prettify()}\n</html>'
    with open(output_file, 'w', encoding='utf-8') as file:
        file.write(new_html)


def merge_html_files(html_files, output_file):
    """合并多个HTML文件并保存为新文件"""
    merged_html = merge_bodies(html_files)
    write_new_html(merged_html, output_file)
    print(f"合并后的HTML文件已保存为: {output_file}")


def remove_nested_container_classes(htmlFile):
    from bs4 import BeautifulSoup

    # 读取原HTML文件内容
    with open(htmlFile, 'r', encoding='utf-8') as file:
        html_content = file.read()

    # 解析HTML内容
    soup = BeautifulSoup(html_content, 'html.parser')

    # 修改内容（示例：移除特定div的class）
    outer_divs = soup.find_all('div', class_='container')
    for outer_div in outer_divs:
        for child in outer_div.find_all('div', recursive=False):
            if 'container' in child.get('class', []):
                child['class'].remove('container')

    # 将修改后的内容写回原文件
    with open(htmlFile, 'w', encoding='utf-8') as file:
        file.write(str(soup))  # 或用 soup.prettify() 格式化输出


if __name__ == "__main__":

    statisticsFilePath = "/home/<USER>/Desktop/generate_report_baidu/autologs/statistics.html"
    changelistDir = "/home/<USER>/Desktop/generate_report_baidu/changelist"

    merge_html_files(
                    [statisticsFilePath, 
                     f"{changelistDir}/ZXSAC-R5300G5-BIOSV04.25.01.20_changelist.html",
                     ],
                     statisticsFilePath)
    remove_nested_container_classes(statisticsFilePath)
    
    # merge_html_files(
    #                 [statisticsFilePath, 
    #                  f"{changelistDir}/ZXSAC-R6500G5-BMCV04.24.04.20_changelist.html",
    #                  f"{changelistDir}/ZXSAC-R6500G5-BIOSV04.24.05.10_changelist.html",
    #                  ],
    #                  statisticsFilePath)
    
    # merge_html_files(
    #                 [statisticsFilePath, 
    #                  f"{changelistDir}/ZXSAC-R5300G5-BMCV04.24.04.20_changelist.html",
    #                  f"{changelistDir}/ZXSAC-R5300G5-BIOSV04.24.05.10_changelist.html",
    #                  ],
    #                  statisticsFilePath)
    
    # merge_html_files(
    #                 [statisticsFilePath, 
    #                  f"{changelistDir}/ZXSAC-R5350G5-BMCV04.24.04.20_changelist.html",
    #                  f"{changelistDir}/ZXSAC-R5350G5-BIOSV24.24.05.00_changelist.html",
    #                  ],
    #                  statisticsFilePath)
