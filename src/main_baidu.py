import os
import re
import copy
import shutil
import xlsx2html
from datetime import datetime
from typing import Union, List

from src.utils.delete_contents import delete_contents, delete_html_files, delete_xlsx_files
from src.utils.download_log import download_testcase_log_by_run_id
from src.utils.parse_log_testcase_info import get_log_testcase_info, get_manual_testcase_info
from src.utils.write_excel import wirte_test_case_result_to_excel, wirte_manual_tc_result_to_excel, write_attachments_files_to_excel
from src.utils.generate_index_html import generate_index_html
from src.utils.generate_statistics_html import generate_statistics_html
from src.utils.clear_log_error import clear_statistics_and_errors_by_folder
from src.utils.generate_all_pass_log import generate_all_pass_log


ALL_TEST_TYPE_LIST = ["BIOS_M", "BMC_M", "DPU", "BF2", "BF3", "STABILITY"]

TEST_SHEET_NAME_SAC_IDX ={
    ("BMC_M", ""): ("1.BMC可运维", 7, 8),
    ("BIOS_M", ""): ("2.BIOS可运维", 7, 8),
    ("DPU", ""): ("3.太行DPU可运维", 7, 8),
    ("BF2", ""): ("4.BF2可运维", 7, 8),
    ("BF3", ""): ("5.BF3可运维", 7, 8),
    ("STABILITY", ""): ("6.稳定性", 7, 8)
}


def generate_report_html_by_run_id(runIds:List, machineType:str="EGS", testTypes:Union[str, List[str]]="ALL",
                                   autologPath:str="./autologs", isClearAutologs:bool=False, hasManualTc=True):
    """ (1)根据测试管理平台的runId, 从ftp下载对应日志文件到本地
        (2)分析日志文件并将测试结果写入测试表格
        (3)生成对应测试报告的html文件
        (4)汇聚所有测试报告html文件到索引html文件
    Args:
        runIds (List): 测试管理测试平台执行ID,用于获取测试报告ftp路径
        machineType (str, optional): 平台类型. 默认值 "EGS". 可以传 "EGS"  "Renoa"  "Whitley"  "Milan"
        testTypes (Union[str, List[str]], optional):测试类型. 默认值 "ALL", 还可以传元素为 "BIOS" "BMC" "REDFISH" "STABILITY" "PERFORMANCE" "FRU" 的列表
        autologPath (str, optional): 自动化日志路径. Defaults to "./autologs".
        isClearAutologs (bool, optional): 是否清除自动化日志路径的文件. Defaults to False.
    """

    # 获取 sheet名 和 用例标识、自动化结果的索引
    sheetInfoList = _get_sheet_info(testTypes, machineType)
    if isClearAutologs:
        # 清空日志文件夹
        delete_contents(autologPath)
    else:
        delete_html_files(autologPath)
    # 下载日志
    for runId in runIds:
        download_testcase_log_by_run_id(runId, "./autologs")
        # clear_statistics_and_errors_by_folder("/home/<USER>/Desktop/leting data/generate_report_baidu/autologs")
        # generate_all_pass_log("/home/<USER>/Desktop/leting data/generate_report_baidu/autologs")
        # os.system("mv /home/<USER>/Desktop/generate_report/autologs_temp/* /home/<USER>/Desktop/generate_report/autologs/")

    # 变量 autologPath 获取所有用例信息
    logTestcasesInfo = get_log_testcase_info(autologPath)
    keyInfoMap = get_testcase_key_info_map(autologPath)

    if hasManualTc:
        manualTcInfo = get_manual_testcase_info("./manual")

    # 生成对应sheet页的html文件
    for sheetName, sacIdx, resultIdx in sheetInfoList:
        resultExcelFile = _generate_test_excel_file(sheetName)
        wirte_test_case_result_to_excel(logTestcasesInfo, resultExcelFile, sheetName, sacIdx, resultIdx, keyInfoMap)
        if hasManualTc:
            wirte_manual_tc_result_to_excel(manualTcInfo, resultExcelFile, sheetName, sacIdx, resultIdx)
        write_attachments_files_to_excel(logTestcasesInfo, resultExcelFile, sheetName, sacIdx)
        htmlFile = resultExcelFile.replace("xlsx", "html")
        xlsx2html.xlsx2html(resultExcelFile, htmlFile, sheet=sheetName)
        replace_html_space(htmlFile)
        fill_in_key_info(htmlFile, keyInfoMap)
        print(f"----- {htmlFile} 已经生成 -----")
    generate_statistics_html(autologPath)
    delete_xlsx_files(autologPath)
    generate_index_html(autologPath)
    replace_index_html_name()


def get_testcase_key_info_map(rootPath):
    keyInfoMap = {}
    for root, _, files in os.walk(rootPath):
        if root.count(os.sep) == rootPath.count(os.sep) + 1:
            hasOutput = 'output.xml' in files
            hasLog = 'log.html' in files
            hasKeyInfo = 'keyInfo.txt' in files
            if hasOutput and hasLog and hasKeyInfo:
                sacId = root.split("/")[-1]
                keyInfoMap.update({sacId: os.path.join(root, "keyInfo.txt")})
    return keyInfoMap


def fill_in_key_info(htmlFile, keyInfoMap):
    if not keyInfoMap:
        return
    isAddTip = False
    with open(htmlFile, 'r', encoding='utf-8') as file:
        html_content = file.read()
    for k, v in keyInfoMap.items():
        if k + "_KEY_INFO" in html_content:
            with open(v, 'r', encoding='utf-8') as file2:
                keyInfoContent = file2.read()
            keyInfo = f"""<div class="tooltip">
                            key info
                            {keyInfoContent}
                        </div>"""
            html_content = re.sub(k + "_KEY_INFO", keyInfo, html_content)
            isAddTip = True
    with open(htmlFile, 'w', encoding='utf-8') as file:
        file.write(html_content)
    if isAddTip:
        add_tool_tip_style(htmlFile)


def replace_html_space(htmlFile):
    with open(htmlFile, 'r', encoding='utf-8') as file:
        html_content = file.read()
    pattern = r'(?<!>)(?<!\})(?<!;)\n'
    html_content = re.sub(pattern, '<br>', html_content)
    with open(htmlFile, 'w', encoding='utf-8') as file:
        file.write(html_content)


def replace_index_html_name():
    from datetime import datetime
    from src.versionInfo import VERSION_INFO
    generateTime = datetime.now().strftime("%Y年%m月%d日")
    os.rename("./index.html", f"{VERSION_INFO['machineType']}-BmcVersion_{VERSION_INFO['bmcVersion']}-biosVersion_{VERSION_INFO['biosVersion']}-{generateTime}.html")


def _get_sheet_info(testTypes, machineType):
    if (testTypes == "ALL" or "BIOS" in testTypes) and not machineType:
        raise Exception("参数 machineType 不能为空！")
    testTypeList = []
    if testTypes == "ALL":
        testTypeList = copy.deepcopy(ALL_TEST_TYPE_LIST)
    elif isinstance(testTypes, list):
        testTypeList = testTypes
    else:
        raise Exception("testTypes 参数填写错误!")
    if not set(testTypeList).issubset(set(ALL_TEST_TYPE_LIST)):
        raise Exception("testTypes 参数填写错误!")
    testItems = []
    for testType in testTypeList:
        if testType == "BIOS":
            testItems.append((testType, machineType))
        else:
            testItems.append((testType, ""))
    sheetInfoList = []
    for testItem in testItems:
        sheetInfoList.append(TEST_SHEET_NAME_SAC_IDX[testItem])
    return sheetInfoList


def _generate_test_excel_file(sheetName):
    timestamp = datetime.now().strftime('%y%m%d%H%M%S')
    sourceFile = './src/templates/百度可运维测试-for oem-v2.3 0507.xlsx'
    # sourceFile = './src/templates/百度可运维测试-0313.xlsx'
    # sourceFile = './src/templates/百度可运维测试-0313-2.xlsx'
    destinationFile = f'./autologs/百度可运维测试_{sheetName}-{timestamp}.xlsx'
    shutil.copy(sourceFile, destinationFile)
    return destinationFile


def add_tool_tip_style(htmlFile):
    from bs4 import BeautifulSoup

    # 读取HTML文件
    with open(htmlFile, "r", encoding="utf-8") as file:
        html_content = file.read()

    soup = BeautifulSoup(html_content, "html.parser")

    style_tag = soup.new_tag("style")
    style_tag.string = """
    .tooltip {
        position: relative;
        display: inline-block;
        cursor: pointer;
    }
    
    .tooltip-content {
        visibility: hidden;
        position: fixed; /* 改为 fixed 定位 */
        z-index: 1000;
        background-color: white;
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        
        /* 设置宽高为视窗的90% */
        width: 90vw;
        max-height: 85vh;
        
        /* 居中定位 */
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        
        /* 其他样式 */
        text-align: left;
        white-space: pre-wrap;       
        word-wrap: break-word;      
        overflow-wrap: break-word;  
        font-family: monospace;
        font-size: 12px;      
        line-height: 1.4;     
        overflow-y: auto;

        /* 添加平滑过渡 */
        transition: visibility 0.2s ease, opacity 0.2s ease;
        opacity: 0;
    }

    /* 内容容器 */
    .tooltip-content-inner {
        width: 80%;
        margin: 0 auto;
    }
    
    .tooltip:hover .tooltip-content {
        visibility: visible;
        opacity: 1;
    }

    /* 添加遮罩背景 */
    .tooltip-content::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0);
        z-index: -1;
    }
        /* 图片样式 */
    .tooltip-image {
        max-width: 100%;  /* 图片最大宽度不超过容器 */
        height: auto;     /* 保持图片比例 */
        display: block;   /* 块级显示避免底部间隙 */
        margin: 10px auto; /* 上下间距和水平居中 */
        border-radius: 4px; /* 圆角效果 */
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 轻微阴影 */
    }
    """

    # 将<style>标签插入到<head>中
    soup.head.append(style_tag)

    # 保存修改后的HTML
    with open(htmlFile, "w", encoding="utf-8") as file:
        file.write(str(soup.prettify()))


if __name__ == "__main__":
    runIds = [62775]
    # 62963,  63164 , 63326 ,  63259 , 63686 
    # runIds = ["71717"]
    # runIds = []
    generate_report_html_by_run_id(runIds,
                                   isClearAutologs=False,
                                   hasManualTc=False)
    print("-----end-----")
